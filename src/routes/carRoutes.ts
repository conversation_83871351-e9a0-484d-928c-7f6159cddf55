import express from 'express';

import {
  createCar,
  createCategory,
  deleteCarCategory,
  deleteCarImages,
  delistCar,
  fetchCarsForClient,
  getBrandsAndCategories,
  getCar,
  getCars,
  updateCar,
  updateCarCategory,
  updateImageVisibility,
  updateMainImage,
  uploadCarImages
} from '../controllers/car_controller.js';
import { handleMulterError, upload } from '../helpers/file_upload.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Import documentation from separate files
// Documentation is now organized in src/docs/ folder for better maintainability

// ===== CAR CATEGORY ROUTES =====

// Create category with image upload
router.post(
  '/category',
  authorize,
  restrictToRoles(['admin']),
  upload.single('image'),
  (err: any, req: any, res: any, next: any) => {
    if (err) {
      return handleMulterError(err, req, res, next);
    }
    next();
  },
  createCategory
);

// Update and delete category
router.put('/category/:id', authorize, restrictToRoles(['admin']), updateCarCategory);
router.delete('/category/:id', authorize, restrictToRoles(['admin']), deleteCarCategory);

// Get brands and categories
router.get('/brands-and-categories', getBrandsAndCategories);

// Get cars for client (with filtering)
router.get('/categories/get', fetchCarsForClient);

// ===== CAR IMAGE ROUTES =====

// Upload car images with proper error handling
router.post(
  '/images',
  authorize,
  restrictToRoles(['admin', 'manager']),
  (req: any, res: any, next: any) => {
    // Add logging to track middleware execution
    console.log('Starting file upload middleware chain for:', req.path);

    // Handle multer upload with error catching
    upload.array('images')(req, res, (err: any) => {
      if (err) {
        console.log('Multer error caught:', err);
        return handleMulterError(err, req, res, next);
      }
      console.log('Multer upload successful, proceeding to uploadCarImages');
      next();
    });
  },
  uploadCarImages
);

// Update image visibility
router.put('/images', authorize, restrictToRoles(['manager', 'admin']), updateImageVisibility);

// Update main image and delete images
router.patch('/images/:id', authorize, restrictToRoles(['admin', 'manager']), updateMainImage);
router.delete('/images/:id', authorize, restrictToRoles(['admin', 'manager']), deleteCarImages);

// ===== CAR MANAGEMENT ROUTES =====

// Create and get cars
router.post('/', authorize, restrictToRoles(['admin', 'manager']), createCar);
router.get('/', authorize, restrictToRoles(['manager', 'admin']), getCars);

// Car CRUD operations
router.get('/:id', authorize, restrictToRoles(['manager', 'admin']), getCar);
router.patch('/:id', authorize, restrictToRoles(['manager', 'admin']), updateCar);
router.delete('/:id', authorize, restrictToRoles(['admin', 'manager']), delistCar);

// Client car access
router.get('/:id/client', authorizeClient, getCar);

export default router;
