/**
 * @swagger
 * /cars/images:
 *   post:
 *     summary: Upload car images
 *     description: Upload multiple images for a car with background processing
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - carId
 *               - images
 *             properties:
 *               carId:
 *                 type: string
 *                 description: Car ID to upload images for
 *                 example: "60d5ecb74b24a1234567890c"
 *               mainImageId:
 *                 type: string
 *                 description: Optional main image identifier
 *                 example: "main-image.jpg"
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Image files (PNG, JPG, JPEG, WebP, max 5MB each)
 *                 maxItems: 10
 *     responses:
 *       200:
 *         description: Images uploaded and processing started
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         carId:
 *                           type: string
 *                           description: Car ID
 *                         filesProcessing:
 *                           type: integer
 *                           description: Number of files being processed
 *                         estimatedProcessingTime:
 *                           type: string
 *                           description: Estimated processing time
 *                         processingTimeMs:
 *                           type: integer
 *                           description: Time taken to schedule uploads
 *       400:
 *         description: Invalid request or file validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               no_files:
 *                 summary: No files uploaded
 *                 value:
 *                   status: "failed"
 *                   message: "Please upload at least one image file."
 *                   error: "NO_FILES_UPLOADED"
 *               missing_car_id:
 *                 summary: Missing car ID
 *                 value:
 *                   status: "failed"
 *                   message: "Car ID is required."
 *                   error: "MISSING_CAR_ID"
 *               file_too_large:
 *                 summary: File size exceeded
 *                 value:
 *                   status: "failed"
 *                   message: "File size too large. Maximum allowed size is 5MB."
 *                   error: "FILE_SIZE_LIMIT_EXCEEDED"
 *               invalid_format:
 *                 summary: Invalid file format
 *                 value:
 *                   status: "failed"
 *                   message: "Invalid file format. Please upload high-quality images (PNG, JPG, JPEG, WebP) only."
 *                   error: "INVALID_FILE_FORMAT"
 *       403:
 *         description: Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               status: "failed"
 *               message: "You do not have permission to upload images for this car."
 *               error: "INSUFFICIENT_PERMISSIONS"
 *       404:
 *         description: Car not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               status: "failed"
 *               message: "Car not found."
 *               error: "CAR_NOT_FOUND"
 *       408:
 *         description: Request timeout
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               status: "failed"
 *               message: "Request timeout. Please try again."
 *               error: "REQUEST_TIMEOUT"
 *       500:
 *         description: Server error during upload processing
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               status: "failed"
 *               message: "Failed to process image uploads. Please try again."
 *               error: "UPLOAD_PROCESSING_ERROR"
 *   put:
 *     summary: Update image visibility
 *     description: Update the visibility status of car images
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageId
 *               - isVisible
 *             properties:
 *               imageId:
 *                 type: string
 *                 description: Image ID
 *               isVisible:
 *                 type: boolean
 *                 description: Visibility status
 *     responses:
 *       200:
 *         description: Image visibility updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 * 
 * /cars/images/{id}:
 *   patch:
 *     summary: Update main image
 *     description: Set an image as the main image for a car
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageId
 *             properties:
 *               imageId:
 *                 type: string
 *                 description: Image ID to set as main
 *     responses:
 *       200:
 *         description: Main image updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Car or image not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete car images
 *     description: Delete specific car images
 *     tags: [Car Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Car ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageIds
 *             properties:
 *               imageIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of image IDs to delete
 *     responses:
 *       200:
 *         description: Images deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Car or images not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// This file contains only Swagger documentation for car image endpoints
// The actual route implementations are in carRoutes.ts
export {};
