/**
 * @swagger
 * components:
 *   schemas:
 *     ClientAuthRequest:
 *       type: object
 *       required:
 *         - phoneNumber
 *       properties:
 *         phoneNumber:
 *           type: string
 *           description: C<PERSON>'s phone number
 *           example: "+2348123456789"
 *
 *     ClientAuthResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: "success"
 *         message:
 *           type: string
 *           example: "OTP sent successfully"
 *         data:
 *           type: object
 *           properties:
 *             token:
 *               type: string
 *               description: JWT token for client authentication
 *             client:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 phoneNumber:
 *                   type: string
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 email:
 *                   type: string
 *
 *     FleetUserRequest:
 *       type: object
 *       required:
 *         - firstName
 *         - lastName
 *         - email
 *         - phoneNumber
 *         - password
 *         - role
 *         - company
 *       properties:
 *         firstName:
 *           type: string
 *           example: "John"
 *         lastName:
 *           type: string
 *           example: "Doe"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         phoneNumber:
 *           type: string
 *           example: "+2348123456789"
 *         password:
 *           type: string
 *           minLength: 6
 *           example: "password123"
 *         role:
 *           type: string
 *           enum: [SUPER_ADMIN, ADMIN, MANAGER]
 *           example: "MANAGER"
 *         company:
 *           type: string
 *           description: Company ID
 *           example: "60d5ecb74b24a1234567890a"
 *
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         password:
 *           type: string
 *           example: "password123"
 *
 *     LoginResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: "success"
 *         message:
 *           type: string
 *           example: "Login successful"
 *         data:
 *           type: object
 *           properties:
 *             token:
 *               type: string
 *               description: JWT token for authentication
 *             user:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *                 company:
 *                   type: string
 *             walletBalance:
 *               type: object
 *               properties:
 *                 currentBalance:
 *                   type: number
 *                   description: Current wallet balance
 *                 grossTotal:
 *                   type: number
 *                   description: Total gross earnings
 *                 netTotal:
 *                   type: number
 *                   description: Total net earnings
 *             meta:
 *               type: object
 *               properties:
 *                 totalBookings:
 *                   type: number
 *                 approvedBookings:
 *                   type: number
 *                 pendingBookings:
 *                   type: number
 *                 declinedBookings:
 *                   type: number
 *                 totalCars:
 *                   type: number
 *                 availableCars:
 *                   type: number
 *                 unavailableCars:
 *                   type: number
 *
 *     OTPRequest:
 *       type: object
 *       required:
 *         - phoneNumber
 *       properties:
 *         phoneNumber:
 *           type: string
 *           example: "+2348123456789"
 *
 *     VerifyOTPRequest:
 *       type: object
 *       required:
 *         - phoneNumber
 *         - otp
 *       properties:
 *         phoneNumber:
 *           type: string
 *           example: "+2348123456789"
 *         otp:
 *           type: string
 *           example: "123456"
 *
 *     ChangePasswordRequest:
 *       type: object
 *       required:
 *         - currentPassword
 *         - newPassword
 *       properties:
 *         currentPassword:
 *           type: string
 *           example: "oldpassword123"
 *         newPassword:
 *           type: string
 *           minLength: 6
 *           example: "newpassword123"
 *
 *     ResetPasswordRequest:
 *       type: object
 *       required:
 *         - newPassword
 *       properties:
 *         newPassword:
 *           type: string
 *           minLength: 6
 *           example: "newpassword123"
 *
 *   tags:
 *     - name: Client Authentication
 *       description: Client authentication and authorization endpoints
 *     - name: Fleet Authentication
 *       description: Fleet user authentication endpoints
 *     - name: Fleet Management
 *       description: Fleet user management endpoints
 *     - name: OTP Management
 *       description: OTP sending and verification endpoints
 *     - name: Password Management
 *       description: Password change and reset endpoints
 *     - name: Authentication
 *       description: General authentication endpoints
 */

/**
 * @swagger
 * /auth/client/authenticate:
 *   post:
 *     summary: Authenticate client with phone number
 *     description: Send OTP to client's phone number for authentication
 *     tags: [Client Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ClientAuthRequest'
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ClientAuthResponse'
 *       400:
 *         description: Invalid phone number
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/client/login-with-token:
 *   post:
 *     summary: Login client with existing token
 *     description: Authenticate client using existing JWT token
 *     tags: [Client Authentication]
 *     security:
 *       - clientAuth: []
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Invalid or expired token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/fleet/create:
 *   post:
 *     summary: Create new fleet user
 *     description: Create a new user for fleet management (admin, manager, etc.)
 *     tags: [Fleet Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/FleetUserRequest'
 *     responses:
 *       201:
 *         description: Fleet user created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: User already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/login:
 *   post:
 *     summary: Fleet user login
 *     description: Authenticate fleet user with email and password
 *     tags: [Fleet Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/logout:
 *   get:
 *     summary: Logout user
 *     description: Logout current authenticated user
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *
 * /auth/send-otp:
 *   post:
 *     summary: Send OTP to phone number
 *     description: Send one-time password to specified phone number
 *     tags: [OTP Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OTPRequest'
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid phone number
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/verify-otp:
 *   post:
 *     summary: Verify OTP
 *     description: Verify one-time password sent to phone number
 *     tags: [OTP Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VerifyOTPRequest'
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid or expired OTP
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/reset-password:
 *   post:
 *     summary: Reset user password
 *     description: Reset password for authenticated user
 *     tags: [Password Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResetPasswordRequest'
 *     responses:
 *       200:
 *         description: Password reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * /auth/change-password:
 *   post:
 *     summary: Change user password
 *     description: Change password for authenticated user
 *     tags: [Password Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChangePasswordRequest'
 *     responses:
 *       200:
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid current password or input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// This file contains only Swagger documentation for auth routes
// The actual route implementations are in authRoutes.ts
export {};
