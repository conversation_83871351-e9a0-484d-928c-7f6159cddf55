/**
 * @swagger
 * components:
 *   schemas:
 *     CreateCarRequest:
 *       type: object
 *       required:
 *         - year
 *         - brandId
 *         - model
 *         - categoryId
 *         - address
 *         - dailyPrice
 *         - dailyMinPrice
 *         - schedule
 *       properties:
 *         year:
 *           type: integer
 *           minimum: 1900
 *           maximum: 2030
 *           description: Car manufacturing year
 *           example: 2022
 *         brandId:
 *           type: string
 *           description: Brand ID reference
 *           example: "60d5ecb74b24a1234567890a"
 *         model:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: Car model name
 *           example: "Camry"
 *         categoryId:
 *           type: string
 *           description: Category ID reference
 *           example: "60d5ecb74b24a1234567890b"
 *         address:
 *           type: object
 *           required:
 *             - street
 *             - city
 *             - state
 *             - coordinates
 *           properties:
 *             street:
 *               type: string
 *               description: Street address
 *               example: "123 Victoria Island"
 *             city:
 *               type: string
 *               description: City name
 *               example: "Lagos"
 *             state:
 *               type: string
 *               description: State name
 *               example: "Lagos"
 *             coordinates:
 *               type: object
 *               required:
 *                 - lat
 *                 - lng
 *               properties:
 *                 lat:
 *                   type: number
 *                   minimum: -90
 *                   maximum: 90
 *                   description: Latitude
 *                   example: 6.4281
 *                 lng:
 *                   type: number
 *                   minimum: -180
 *                   maximum: 180
 *                   description: Longitude
 *                   example: 3.4219
 *         dailyPrice:
 *           type: number
 *           minimum: 0
 *           description: Daily rental price in Naira
 *           example: 25000
 *         dailyMinPrice:
 *           type: number
 *           minimum: 0
 *           description: Minimum daily price in Naira
 *           example: 20000
 *         schedule:
 *           type: string
 *           enum: [day, night]
 *           description: Car availability schedule
 *           example: "day"
 *
 *     Car:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Car unique identifier
 *         year:
 *           type: integer
 *           description: Car manufacturing year
 *         brand:
 *           type: object
 *           description: Car brand information
 *         model:
 *           type: string
 *           description: Car model name
 *         category:
 *           type: object
 *           description: Car category information
 *         address:
 *           type: object
 *           description: Car location address
 *         dailyPrice:
 *           type: number
 *           description: Daily rental price
 *         dailyMinPrice:
 *           type: number
 *           description: Minimum daily price
 *         schedule:
 *           type: string
 *           description: Car availability schedule
 *         carImages:
 *           type: array
 *           items:
 *             type: object
 *           description: Car images
 *         isActive:
 *           type: boolean
 *           description: Car active status
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *
 *     CreateCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: Category name
 *           example: "Luxury"
 *
 *     UpdateCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           description: Updated category name
 *           example: "Premium Luxury"
 *
 *     BrandsAndCategoriesResponse:
 *       type: object
 *       properties:
 *         brands:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               _id:
 *                 type: string
 *               name:
 *                 type: string
 *         categories:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               _id:
 *                 type: string
 *               name:
 *                 type: string
 *               thumbnailUrl:
 *                 type: string
 *               mediumUrl:
 *                 type: string
 *               bigUrl:
 *                 type: string
 *
 *   tags:
 *     - name: Car Categories
 *       description: Car category management endpoints
 *     - name: Car Images
 *       description: Car image upload and management endpoints
 *     - name: Car Management
 *       description: Car CRUD operations for admin/manager
 *     - name: Client Cars
 *       description: Car endpoints for client applications
 */

/**
 * @swagger
 * /cars/categories:
 *   post:
 *     summary: Create a new car category
 *     description: Create a new category for cars with image upload
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - image
 *             properties:
 *               name:
 *                 type: string
 *                 description: Category name
 *                 example: "Luxury"
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Category image file (PNG, JPG, JPEG, WebP)
 *     responses:
 *       201:
 *         description: Category created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         imageProcessing:
 *                           type: boolean
 *                         estimatedProcessingTime:
 *                           type: string
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   get:
 *     summary: Get brands and categories
 *     description: Retrieve all car brands and categories
 *     tags: [Car Categories]
 *     responses:
 *       200:
 *         description: Brands and categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/BrandsAndCategoriesResponse'
 *
 * /cars/categories/{id}:
 *   patch:
 *     summary: Update car category
 *     description: Update an existing car category
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCategoryRequest'
 *     responses:
 *       200:
 *         description: Category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete car category
 *     description: Delete an existing car category
 *     tags: [Car Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     responses:
 *       200:
 *         description: Category deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// This file contains only Swagger documentation for car routes
// The actual route implementations are in carRoutes.ts
export {};
